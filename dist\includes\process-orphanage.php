<?php
session_start();

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
  header('Location: ../../index.php');
  exit;
}

include 'connection.php';

// Add new orphanage
if (isset($_POST['add_orphanage'])) {
  $name = mysqli_real_escape_string($db, $_POST['name']);
  $location = mysqli_real_escape_string($db, $_POST['location']);
  $description = mysqli_real_escape_string($db, $_POST['description']);
  $contact_person = mysqli_real_escape_string($db, $_POST['contact_person']);
  $contact_phone = mysqli_real_escape_string($db, $_POST['contact_phone']);
  $contact_email = mysqli_real_escape_string($db, $_POST['contact_email']);
  $bank_account = mysqli_real_escape_string($db, $_POST['bank_account']);

  // Validate required fields
  if (empty($name) || empty($location) || empty($description) || empty($contact_person) || empty($contact_phone) || empty($contact_email)) {
    header('Location: ../pages/admin/orphanages.php?error=All fields except bank account are required');
    exit;
  }

  // Validate email format
  if (!filter_var($contact_email, FILTER_VALIDATE_EMAIL)) {
    header('Location: ../pages/admin/orphanages.php?error=Please enter a valid email address');
    exit;
  }

  // Check if orphanage name already exists
  $check_sql = "SELECT id FROM orphanages WHERE name = '$name'";
  $check_result = mysqli_query($db, $check_sql);
  if (mysqli_num_rows($check_result) > 0) {
    header('Location: ../pages/admin/orphanages.php?error=An orphanage with this name already exists');
    exit;
  }

  $sql = "INSERT INTO orphanages (name, location, description, contact_person, contact_phone, contact_email, bank_account)
          VALUES ('$name', '$location', '$description', '$contact_person', '$contact_phone', '$contact_email', '$bank_account')";

  if (mysqli_query($db, $sql)) {
    $new_orphanage_id = mysqli_insert_id($db);
    header('Location: ../pages/admin/orphanages.php?success=Orphanage added successfully with ID: ' . $new_orphanage_id);
  } else {
    header('Location: ../pages/admin/orphanages.php?error=Failed to add orphanage: ' . mysqli_error($db));
  }
}

// Update orphanage
if (isset($_POST['update_orphanage'])) {
  $id = mysqli_real_escape_string($db, $_POST['orphanage_id']);
  $name = mysqli_real_escape_string($db, $_POST['name']);
  $location = mysqli_real_escape_string($db, $_POST['location']);
  $description = mysqli_real_escape_string($db, $_POST['description']);
  $contact_person = mysqli_real_escape_string($db, $_POST['contact_person']);
  $contact_phone = mysqli_real_escape_string($db, $_POST['contact_phone']);
  $contact_email = mysqli_real_escape_string($db, $_POST['contact_email']);
  $bank_account = mysqli_real_escape_string($db, $_POST['bank_account']);

  // Validate required fields
  if (empty($id) || empty($name) || empty($location) || empty($description) || empty($contact_person) || empty($contact_phone) || empty($contact_email)) {
    header('Location: ../pages/admin/edit-orphanage.php?id=' . $id . '&error=All fields except bank account are required');
    exit;
  }

  // Validate email format
  if (!filter_var($contact_email, FILTER_VALIDATE_EMAIL)) {
    header('Location: ../pages/admin/edit-orphanage.php?id=' . $id . '&error=Please enter a valid email address');
    exit;
  }

  // Check if orphanage name already exists (excluding current orphanage)
  $check_sql = "SELECT id FROM orphanages WHERE name = '$name' AND id != '$id'";
  $check_result = mysqli_query($db, $check_sql);
  if (mysqli_num_rows($check_result) > 0) {
    header('Location: ../pages/admin/edit-orphanage.php?id=' . $id . '&error=An orphanage with this name already exists');
    exit;
  }

  $sql = "UPDATE orphanages SET
          name = '$name',
          location = '$location',
          description = '$description',
          contact_person = '$contact_person',
          contact_phone = '$contact_phone',
          contact_email = '$contact_email',
          bank_account = '$bank_account',
          updated_at = CURRENT_TIMESTAMP
          WHERE id = '$id'";

  if (mysqli_query($db, $sql)) {
    header('Location: ../pages/admin/orphanages.php?success=Orphanage updated successfully');
  } else {
    header('Location: ../pages/admin/edit-orphanage.php?id=' . $id . '&error=Failed to update orphanage: ' . mysqli_error($db));
  }
}

// Delete orphanage
if (isset($_POST['delete_orphanage'])) {
  $id = mysqli_real_escape_string($db, $_POST['orphanage_id']);

  if (empty($id)) {
    header('Location: ../pages/admin/orphanages.php?error=Invalid orphanage ID');
    exit;
  }

  // Check if there are donations associated with this orphanage
  $check_donations_sql = "SELECT COUNT(*) as count FROM donations WHERE orphanage_id = '$id'";
  $donations_result = mysqli_query($db, $check_donations_sql);
  $donations_row = mysqli_fetch_assoc($donations_result);

  if ($donations_row['count'] > 0) {
    header('Location: ../pages/admin/orphanages.php?error=Cannot delete orphanage because it has ' . $donations_row['count'] . ' associated donations. Consider marking it as inactive instead.');
    exit;
  }

  // Check if there are campaigns associated with this orphanage
  $check_campaigns_sql = "SELECT COUNT(*) as count FROM campaigns WHERE orphanage_id = '$id'";
  $campaigns_result = mysqli_query($db, $check_campaigns_sql);
  $campaigns_row = mysqli_fetch_assoc($campaigns_result);

  if ($campaigns_row['count'] > 0) {
    header('Location: ../pages/admin/orphanages.php?error=Cannot delete orphanage because it has ' . $campaigns_row['count'] . ' associated campaigns. Please delete campaigns first.');
    exit;
  }

  $sql = "DELETE FROM orphanages WHERE id = '$id'";

  if (mysqli_query($db, $sql)) {
    header('Location: ../pages/admin/orphanages.php?success=Orphanage deleted successfully');
  } else {
    header('Location: ../pages/admin/orphanages.php?error=Failed to delete orphanage: ' . mysqli_error($db));
  }
}

// If no action was performed, redirect back to orphanages page
header('Location: ../pages/admin/orphanages.php');