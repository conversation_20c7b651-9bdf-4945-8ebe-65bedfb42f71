<?php
session_start();
include 'connection.php';

if (!isset($_SESSION['user_id'])) {
  header('Location: ../../index.php');
  exit;
}

if (isset($_POST['process_donation'])) {
  $user_id = $_SESSION['user_id'];
  $orphanage_id = mysqli_real_escape_string($db, $_POST['orphanage_id']);
  $campaign_id = isset($_POST['campaign_id']) ? mysqli_real_escape_string($db, $_POST['campaign_id']) : null;
  $amount = mysqli_real_escape_string($db, $_POST['amount']);
  $payment_method = mysqli_real_escape_string($db, $_POST['payment_method']);
  $message = mysqli_real_escape_string($db, $_POST['message']);

  // Build redirect URL for errors
  $redirect_url = '../../make-donation.php?orphanage_id=' . $orphanage_id;
  if ($campaign_id) {
    $redirect_url .= '&campaign_id=' . $campaign_id;
  }

  // Validate inputs
  if (empty($amount) || !is_numeric($amount) || $amount <= 0) {
    header('Location: ' . $redirect_url . '&error=Please enter a valid amount');
    exit;
  }

  if (empty($payment_method)) {
    header('Location: ' . $redirect_url . '&error=Please select a payment method');
    exit;
  }

  // If campaign_id is provided, validate it
  if ($campaign_id) {
    $campaign_check = "SELECT id, status FROM campaigns WHERE id = '$campaign_id' AND orphanage_id = '$orphanage_id'";
    $campaign_result = mysqli_query($db, $campaign_check);

    if (mysqli_num_rows($campaign_result) == 0) {
      header('Location: ' . $redirect_url . '&error=Invalid campaign selected');
      exit;
    }

    $campaign_data = mysqli_fetch_assoc($campaign_result);
    if ($campaign_data['status'] !== 'active') {
      header('Location: ' . $redirect_url . '&error=This campaign is no longer active');
      exit;
    }
  }

  // In a real application, you would process the payment here
  // For this example, we'll simulate a successful payment
  $transaction_id = 'TXN' . time() . rand(1000, 9999);

  // Insert donation record
  $campaign_field = $campaign_id ? ", campaign_id" : "";
  $campaign_value = $campaign_id ? ", '$campaign_id'" : "";

  $sql = "INSERT INTO donations (user_id, orphanage_id{$campaign_field}, amount, payment_status, payment_method, transaction_id, message)
          VALUES ('$user_id', '$orphanage_id'{$campaign_value}, '$amount', 'completed', '$payment_method', '$transaction_id', '$message')";

  if (mysqli_query($db, $sql)) {
    // If this is a campaign donation, update the campaign's current amount
    if ($campaign_id) {
      $update_campaign = "UPDATE campaigns SET current_amount = current_amount + $amount WHERE id = '$campaign_id'";
      mysqli_query($db, $update_campaign);

      // Check if campaign target is reached and mark as completed
      $check_completion = "SELECT target_amount, current_amount FROM campaigns WHERE id = '$campaign_id'";
      $completion_result = mysqli_query($db, $check_completion);
      $completion_data = mysqli_fetch_assoc($completion_result);

      if ($completion_data['current_amount'] >= $completion_data['target_amount']) {
        $complete_campaign = "UPDATE campaigns SET status = 'completed' WHERE id = '$campaign_id'";
        mysqli_query($db, $complete_campaign);
      }
    }

    header('Location: ../../donation-success.php?transaction_id=' . $transaction_id);
  } else {
    header('Location: ' . $redirect_url . '&error=Failed to process donation. Please try again.');
  }
} else {
  header('Location: ../../orphanages.php');
}