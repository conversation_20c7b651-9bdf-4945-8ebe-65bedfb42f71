<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" width="100" height="100">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f5576c;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="50" cy="50" r="48" fill="white" stroke="url(#gradient)" stroke-width="2"/>
  
  <!-- Simplified hands -->
  <path d="M 15 60 Q 10 50 15 45 Q 25 40 35 50 L 40 60 Q 45 70 50 65 Q 55 70 60 60 L 65 50 Q 75 40 85 45 Q 90 50 85 60 L 75 70 Q 65 75 50 70 Q 35 75 25 70 Z" 
        fill="url(#gradient)" opacity="0.8"/>
  
  <!-- Children (simplified) -->
  <circle cx="50" cy="45" r="8" fill="url(#gradient)"/>
  <ellipse cx="50" cy="60" rx="10" ry="15" fill="url(#gradient)"/>
  
  <!-- Heart -->
  <path d="M 50 25 C 47 22, 42 22, 42 28 C 42 34, 50 40, 50 40 C 50 40, 58 34, 58 28 C 58 22, 53 22, 50 25 Z" 
        fill="#fa709a"/>
</svg>
