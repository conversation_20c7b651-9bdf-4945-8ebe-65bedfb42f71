<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 300 80" width="300" height="80">
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f093fb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f5576c;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Logo circle -->
  <circle cx="40" cy="40" r="35" fill="white" stroke="url(#logoGradient)" stroke-width="2"/>
  
  <!-- Hands -->
  <path d="M 15 45 Q 12 40 15 37 Q 20 35 25 40 L 30 45 Q 32 50 35 48 Q 38 50 40 45 L 45 40 Q 50 35 55 37 Q 58 40 55 45 L 50 50 Q 45 52 40 50 Q 35 52 30 50 Z" 
        fill="url(#logoGradient)" opacity="0.8"/>
  
  <!-- Child -->
  <circle cx="40" cy="35" r="6" fill="url(#textGradient)"/>
  <ellipse cx="40" cy="45" rx="7" ry="10" fill="url(#textGradient)"/>
  
  <!-- Heart -->
  <path d="M 40 25 C 38 23, 35 23, 35 26 C 35 29, 40 33, 40 33 C 40 33, 45 29, 45 26 C 45 23, 42 23, 40 25 Z" 
        fill="#fa709a"/>
  
  <!-- Text -->
  <text x="90" y="35" font-family="Poppins, Arial, sans-serif" font-size="24" font-weight="700" fill="url(#logoGradient)">
    TumainiFuraha
  </text>
  <text x="90" y="55" font-family="Poppins, Arial, sans-serif" font-size="12" font-weight="400" fill="#666">
    Hope and Joy for Every Child
  </text>
</svg>
