      <!--Header-->
      <?php
      include 'header.php';

      // Include database connection
      require_once '../../includes/connection.php';

      // Get donation statistics for the logged-in user
      $user_id = $_SESSION['user_id'];

      // Count total donations
      $stmt = $db->prepare("SELECT COUNT(*) as total_donations FROM donations WHERE user_id = ?");
      $stmt->bind_param('i', $user_id);
      $stmt->execute();
      $result = $stmt->get_result();
      $donation_stats = $result->fetch_assoc();
      $total_donations = $donation_stats['total_donations'];

      // Get total amount donated
      $stmt = $db->prepare("SELECT SUM(amount) as total_amount FROM donations WHERE user_id = ? AND payment_status = 'completed'");
      $stmt->bind_param('i', $user_id);
      $stmt->execute();
      $result = $stmt->get_result();
      $amount_stats = $result->fetch_assoc();
      $total_amount = $amount_stats['total_amount'] ?? 0;

      // Count successful donations
      $stmt = $db->prepare("SELECT COUNT(*) as successful_donations FROM donations WHERE user_id = ? AND payment_status = 'completed'");
      $stmt->bind_param('i', $user_id);
      $stmt->execute();
      $result = $stmt->get_result();
      $success_stats = $result->fetch_assoc();
      $successful_donations = $success_stats['successful_donations'];

      // Count orphanages helped
      $stmt = $db->prepare("SELECT COUNT(DISTINCT orphanage_id) as orphanages_helped FROM donations WHERE user_id = ? AND payment_status = 'completed'");
      $stmt->bind_param('i', $user_id);
      $stmt->execute();
      $result = $stmt->get_result();
      $orphanage_stats = $result->fetch_assoc();
      $orphanages_helped = $orphanage_stats['orphanages_helped'];
      ?>
      <!--end::Header-->

      <!--begin::Sidebar-->
      <?php include 'sidebar.php' ?>
      <!--end::Sidebar-->

      <!--begin::App Main-->
      <main class="app-main">
        <!--begin::App Content Header-->
        <div class="app-content-header">
          <!--begin::Container-->
          <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
              <div class="col-sm-6"><h3 class="mb-0">Dashboard</h3></div>
              <div class="col-sm-6">
                <ol class="breadcrumb float-sm-end">
                  <li class="breadcrumb-item"><a href="#">Home</a></li>
                  <li class="breadcrumb-item active" aria-current="page">Dashboard</li>
                </ol>
              </div>
            </div>
            <!--end::Row-->
          </div>
          <!--end::Container-->
        </div>
        <!--end::App Content Header-->
        <!--begin::App Content-->
        <div class="app-content">
          <!--begin::Container-->
          <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
              <!--begin::Col-->
              <div class="col-lg-3 col-6">
                <!--begin::Small Box Widget 1-->
                <div class="small-box text-bg-primary">
                  <div class="inner">
                    <h3><?php echo $total_donations; ?></h3>
                    <p>Total Donations</p>
                  </div>
                  <svg
                    class="small-box-icon"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                    aria-hidden="true"
                  >
                    <path
                      d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"
                    ></path>
                  </svg>
                  <a
                    href="donations.php"
                    class="small-box-footer link-light link-underline-opacity-0 link-underline-opacity-50-hover"
                  >
                    View Details <i class="bi bi-link-45deg"></i>
                  </a>
                </div>
                <!--end::Small Box Widget 1-->
              </div>
              <!--end::Col-->

              <!--begin::Col-->
              <div class="col-lg-3 col-6">
                <!--begin::Small Box Widget 2-->
                <div class="small-box text-bg-success">
                  <div class="inner">
                    <h3>$<?php echo number_format($total_amount, 2); ?></h3>
                    <p>Total Amount Donated</p>
                  </div>
                  <svg
                    class="small-box-icon"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                    aria-hidden="true"
                  >
                    <path
                      d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1.41 16.09V20h-2.67v-1.93c-1.71-.36-3.16-1.46-3.27-3.4h1.96c.1 1.05.82 1.87 2.65 1.87 1.96 0 2.4-.98 2.4-1.59 0-.83-.44-1.61-2.67-2.14-2.48-.6-4.18-1.62-4.18-3.67 0-1.72 1.39-2.84 3.11-3.21V4h2.67v1.95c1.86.45 2.79 1.86 2.85 3.39H14.3c-.05-1.11-.64-1.87-2.22-1.87-1.5 0-2.4.68-2.4 1.64 0 .84.65 1.39 2.67 1.91s4.18 1.39 4.18 3.91c-.01 1.83-1.38 2.83-3.12 3.16z"
                    ></path>
                  </svg>
                  <a
                    href="donations.php"
                    class="small-box-footer link-light link-underline-opacity-0 link-underline-opacity-50-hover"
                  >
                    View Details <i class="bi bi-link-45deg"></i>
                  </a>
                </div>
                <!--end::Small Box Widget 2-->
              </div>
              <!--end::Col-->

              <!--begin::Col-->
              <div class="col-lg-3 col-6">
                <!--begin::Small Box Widget 3-->
                <div class="small-box text-bg-warning">
                  <div class="inner">
                    <h3><?php echo $successful_donations; ?></h3>
                    <p>Successful Donations</p>
                  </div>
                  <svg
                    class="small-box-icon"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                    aria-hidden="true"
                  >
                    <path
                      d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"
                    ></path>
                  </svg>
                  <a
                    href="donations.php"
                    class="small-box-footer link-dark link-underline-opacity-0 link-underline-opacity-50-hover"
                  >
                    View Details <i class="bi bi-link-45deg"></i>
                  </a>
                </div>
                <!--end::Small Box Widget 3-->
              </div>
              <!--end::Col-->

              <!--begin::Col-->
              <div class="col-lg-3 col-6">
                <!--begin::Small Box Widget 4-->
                <div class="small-box text-bg-info">
                  <div class="inner">
                    <h3><?php echo $orphanages_helped; ?></h3>
                    <p>Orphanages Helped</p>
                  </div>
                  <svg
                    class="small-box-icon"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                    aria-hidden="true"
                  >
                    <path
                      d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                    ></path>
                  </svg>
                  <a
                    href="../../../orphanages.php"
                    class="small-box-footer link-light link-underline-opacity-0 link-underline-opacity-50-hover"
                  >
                    View Orphanages <i class="bi bi-link-45deg"></i>
                  </a>
                </div>
                <!--end::Small Box Widget 4-->
              </div>
              <!--end::Col-->

            </div>
            <!--end::Row-->

            <!--begin::Recent Donations Row-->
            <div class="row mt-4">
              <div class="col-12">
                <div class="card">
                  <div class="card-header">
                    <h3 class="card-title">
                      <i class="bi bi-heart-fill me-2"></i>
                      Recent Donations
                    </h3>
                    <div class="card-tools">
                      <a href="donations.php" class="btn btn-primary btn-sm">
                        <i class="bi bi-list-ul me-1"></i>
                        View All
                      </a>
                    </div>
                  </div>
                  <div class="card-body p-0">
                    <?php
                    // Get recent donations for the user
                    $stmt = $db->prepare("
                      SELECT d.*, o.name as orphanage_name, o.location, c.title as campaign_title
                      FROM donations d
                      LEFT JOIN orphanages o ON d.orphanage_id = o.id
                      LEFT JOIN campaigns c ON d.campaign_id = c.id
                      WHERE d.user_id = ?
                      ORDER BY d.created_at DESC
                      LIMIT 5
                    ");
                    $stmt->bind_param('i', $user_id);
                    $stmt->execute();
                    $recent_donations = $stmt->get_result();

                    if ($recent_donations->num_rows > 0) {
                    ?>
                    <div class="table-responsive">
                      <table class="table table-striped">
                        <thead>
                          <tr>
                            <th>Date</th>
                            <th>Orphanage</th>
                            <th>Campaign</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Payment Method</th>
                          </tr>
                        </thead>
                        <tbody>
                          <?php while ($donation = $recent_donations->fetch_assoc()) { ?>
                          <tr>
                            <td>
                              <small class="text-muted">
                                <?php echo date('M j, Y', strtotime($donation['created_at'])); ?>
                              </small>
                            </td>
                            <td>
                              <strong><?php echo htmlspecialchars($donation['orphanage_name']); ?></strong>
                              <br>
                              <small class="text-muted"><?php echo htmlspecialchars($donation['location']); ?></small>
                            </td>
                            <td>
                              <?php if ($donation['campaign_title']) { ?>
                                <span class="badge bg-info"><?php echo htmlspecialchars($donation['campaign_title']); ?></span>
                              <?php } else { ?>
                                <span class="text-muted">General Donation</span>
                              <?php } ?>
                            </td>
                            <td>
                              <strong class="text-success">$<?php echo number_format($donation['amount'], 2); ?></strong>
                            </td>
                            <td>
                              <?php
                              $status_class = '';
                              $status_text = '';
                              switch ($donation['payment_status']) {
                                case 'completed':
                                  $status_class = 'bg-success';
                                  $status_text = 'Completed';
                                  break;
                                case 'pending':
                                  $status_class = 'bg-warning';
                                  $status_text = 'Pending';
                                  break;
                                case 'failed':
                                  $status_class = 'bg-danger';
                                  $status_text = 'Failed';
                                  break;
                                default:
                                  $status_class = 'bg-secondary';
                                  $status_text = ucfirst($donation['payment_status']);
                              }
                              ?>
                              <span class="badge <?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                            </td>
                            <td>
                              <?php
                              $payment_methods = [
                                'credit_card' => 'Credit Card',
                                'paypal' => 'PayPal',
                                'bank_transfer' => 'Bank Transfer',
                                'mpesa' => 'M-Pesa'
                              ];
                              echo $payment_methods[$donation['payment_method']] ?? ucfirst($donation['payment_method']);
                              ?>
                            </td>
                          </tr>
                          <?php } ?>
                        </tbody>
                      </table>
                    </div>
                    <?php } else { ?>
                    <div class="text-center py-5">
                      <i class="bi bi-heart text-muted" style="font-size: 3rem;"></i>
                      <h5 class="mt-3 text-muted">No donations yet</h5>
                      <p class="text-muted">Start making a difference by donating to orphanages in need.</p>
                      <a href="../../../orphanages.php" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-1"></i>
                        Make Your First Donation
                      </a>
                    </div>
                    <?php } ?>
                  </div>
                </div>
              </div>
            </div>
            <!--end::Recent Donations Row-->
          </div>
          <!--end::Container-->
        </div>
        <!--end::App Content-->
      </main>
      <!--end::App Main-->

      <!--begin::Footer-->
       <?php include 'footer.php' ?>
      <!--end::Footer-->
    