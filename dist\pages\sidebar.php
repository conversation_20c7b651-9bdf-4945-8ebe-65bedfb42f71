<?php
  // Determine the correct base path based on current location
  $current_page = basename($_SERVER['PHP_SELF']);
  $current_dir = dirname($_SERVER['SCRIPT_NAME']);

  // Check if we're in admin directory
  $is_admin = strpos($current_dir, '/admin') !== false;

  // Set base paths accordingly
  if ($is_admin) {
    $base_path = '../';
    $admin_path = '';
  } else {
    $base_path = '';
    $admin_path = 'admin/';
  }
?>
<aside class="app-sidebar bg-body-secondary shadow" data-bs-theme="dark">
  <!--begin::Sidebar Brand-->
  <div class="sidebar-brand">
    <!--begin::Brand Link-->
    <a href="<?php echo $base_path . 'index.php'; ?>" class="brand-link">
      <!--begin::Brand Icon-->
      <i class="bi bi-heart-fill brand-image text-danger" style="font-size: 2rem; margin-right: 0.5rem;"></i>
      <!--end::Brand Icon-->
      <!--begin::Brand Text-->
      <span class="brand-text fw-bold">Donation Platform</span>
      <!--end::Brand Text-->
    </a>
    <!--end::Brand Link-->
  </div>
  <!--end::Sidebar Brand-->
  <!--begin::Sidebar Wrapper-->
  <div class="sidebar-wrapper">
    <nav class="mt-2">
      <!--begin::Sidebar Menu-->
      <ul
        class="nav sidebar-menu flex-column"
        data-lte-toggle="treeview"
        role="menu"
        data-accordion="false"
      >
        <li class="nav-item menu-open">
          <a href="<?php echo $base_path . ($is_admin ? '../' : '') . 'index.php'; ?>" class="nav-link<?php echo ($current_page == 'index.php') ? ' active' : ''; ?>">
            <i class="nav-icon bi bi-speedometer2"></i>
            <p>
              Dashboard
            </p>
          </a>
        </li>
        <li class="nav-item menu-open">
          <a href="<?php echo $admin_path . 'orphanages.php'; ?>" class="nav-link<?php echo ($current_page == 'orphanages.php') ? '' : ''; ?>">
            <i class="nav-icon bi bi-house-heart"></i>
            <p>
              Orphanages
            </p>
          </a>
        </li>
        <li class="nav-item menu-open">
          <a href="<?php echo $admin_path . 'campaigns.php'; ?>" class="nav-link<?php echo ($current_page == 'campaigns.php') ? ' active' : ''; ?>">
            <i class="nav-icon bi bi-megaphone"></i>
            <p>
              Campaigns
            </p>
          </a>
        </li>
        <li class="nav-item menu-open">
          <a href="<?php echo $admin_path . 'donors.php'; ?>" class="nav-link<?php echo ($current_page == 'donors.php') ? ' active' : ''; ?>">
            <i class="nav-icon bi bi-people"></i>
            <p>
              Donors
            </p>
          </a>
        </li>
        <li class="nav-item menu-open">
          <a href="<?php echo $admin_path . 'donations.php'; ?>" class="nav-link<?php echo ($current_page == 'donations.php') ? ' active' : ''; ?>">
            <i class="nav-icon bi bi-heart-fill"></i>
            <p>
              Donations
            </p>
          </a>
        </li>
        <li class="nav-item menu-open">
          <a href="<?php echo $base_path . ($is_admin ? '../' : '') . 'index.php'; ?>" class="nav-link" target="_blank">
            <i class="nav-icon bi bi-globe"></i>
            <p>
              View Public Site
            </p>
          </a>
        </li>
      </ul>
      <!--end::Sidebar Menu-->
    </nav>
  </div>
  <!--end::Sidebar Wrapper-->
</aside>